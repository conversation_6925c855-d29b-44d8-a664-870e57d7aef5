{"name": "xml-js", "version": "1.6.11", "description": "A convertor between XML text and Javascript object / JSON text.", "repository": {"type": "git", "url": "git+https://github.com/nashwaan/xml-js.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/nashwaan/xml-js/issues"}, "homepage": "https://github.com/nashwaan/xml-js#readme", "keywords": ["XML", "xml", "js", "JSON", "json", "cdata", "CDATA", "doctype", "processing instruction", "Javascript", "js2xml", "json2xml", "xml2js", "xml2json", "transform", "transformer", "transforming", "transformation", "convert", "convertor", "converting", "conversion", "parse", "parser", "parsing"], "main": "lib/index.js", "bin": "./bin/cli.js", "types": "./types/index.d.ts", "scripts": {"build": "webpack", "doc": "node doc/compile-doc.js", "watch:doc": "watch \"npm run doc\" doc/templates/", "live:doc": "browser-sync start --port 9997 --server doc/ --files doc/templates/ --no-open --no-ui --no-online", "open:doc": "biased-opener --browser chrome http://localhost:9997", "start:doc": "npm-run-all --parallel watch:doc live:doc open:doc", "debug": "nodemon --inspect --watch lib/ --watch test/ --debug-brk test/index.js", "debug:cli": "nodemon --inspect --watch lib/ --debug-brk index.js -- --help", "jest": "jest --config=test/jest.conf.js", "jasmine": "jasmine JASMINE_CONFIG_PATH=./test/jasmine.json", "watch:jasmine": "watch \"npm run jasmine\" lib/ test/ --ignoreDirectoryPattern=/browse-.+/", "bundle:jasmine": "globify test/*.spec.js --watch --verbose --list --outfile test/browse-jasmine/bundle.js", "live:jasmine": "browser-sync start --port 9999 --server test/browse-jasmine/ --files test/browse-jasmine/ --no-open --no-ui --no-online", "open-help": "biased-opener --help", "open:jasmine": "biased-opener --browser chrome http://localhost:9999", "istanbul-original": "istanbul cover --dir test/coverage-jasmine -x test/browse-** test/index.js", "istanbul": "istanbul cover --dir test/coverage-jasmine test/index.js", "watch:istanbul": "watch \"npm run istanbul\" lib/ test/ --ignoreDirectoryPattern=/coverage-.+/", "live:istanbul": "browser-sync start --port 9998 --server test/coverage-jasmine/lcov-report/ --files test/coverage-jasmine/lcov-report/ --no-open --no-ui --no-online", "open:istanbul": "biased-opener --browser chrome http://localhost:9998", "live": "npm-run-all --parallel live:* open:*", "start": "npm-run-all --parallel bundle:jasmine live:jasmine open:jasmine watch:istanbul live:istanbul open:istanbul", "git:commit": "git add . && git commit -a -m \"Committed by npm script.\" && git push origin master", "git:push": "git push origin master", "deploy": "npm-run-all --serial coverage:* git:*", "coverage": "npm-run-all coverage:*", "coverage:a-step": "npm run istanbul", "coverage:coveralls": "cross-env COVERALLS_REPO_TOKEN=CaEwzjHxsKRqomJSYmGagrJdlR7uLHhHC && cat ./test/coverage-jasmine/lcov.info | coveralls", "coverage:codecov": "codecov --token=0e52af41-702b-4d7f-8aa3-61145ac36624 --file=test/coverage-jasmine/lcov.info ", "coverage:codacy": "cross-env CODACY_PROJECT_TOKEN=0207815122ea49a68241d1aa435f21f1 && cat ./test/coverage-jasmine/lcov.info | codacy-coverage", "coverage:codeclimate": "cross-env CODECLIMATE_REPO_TOKEN=60848a077f9070acf358b0c7145f0a2698a460ddeca7d8250815e75aa4333f7d codeclimate-test-reporter < test\\coverage-jasmine\\lcov.info", "prepublish": "npm run test", "test": "npm run jasmine && npm run jest && npm run test:types", "test:types": "tsc -p ./types"}, "dependencies": {"sax": "^1.2.4"}, "devDependencies": {"babel-core": "^6.26.3", "babel-loader": "^7.1.4", "babel-preset-env": "^1.7.0", "biased-opener": "^0.2.8", "browser-sync": "^2.26.3", "cash-cat": "^0.2.0", "codacy-coverage": "^3.4.0", "codeclimate-test-reporter": "^0.5.1", "codecov": "^3.1.0", "coveralls": "^3.0.2", "cross-env": "^5.2.0", "eslint": "^5.12.0", "globify": "^2.3.4", "istanbul": "^0.4.5", "jasmine": "^3.3.1", "jest": "^20.0.4", "jest-cli": "^20.0.4", "jsonpath": "^1.0.0", "nodemon": "^1.18.9", "npm-run-all": "^4.1.5", "prismjs": "^1.15.0", "typescript": "^3.2.2", "unminified-webpack-plugin": "^1.4.2", "watch": "^1.0.1", "webpack": "^3.10.0"}}