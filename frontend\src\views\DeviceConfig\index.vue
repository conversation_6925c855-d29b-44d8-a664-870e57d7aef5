<template>
  <div class="body_wrap">
    <div class="p_wrap">
      <div class="title_wrap">
        <!-- <a-breadcrumb>
          <a-breadcrumb-item>返回</a-breadcrumb-item>
          <a-breadcrumb-item><a href="">项目名称</a></a-breadcrumb-item>
        </a-breadcrumb> -->
        <!-- <div class="btn_wrap">
          <a-button class="btn_item" size="small"  type="primary" @click="createNewPro">新建项目</a-button>
        </div> -->
      </div>
      <div class="content_wrap" id="content_wrap">
        <div class="part_wrap">
          <div class="p_title">
            <div>设备配置</div>
            <div class="btn_wrap">
              <!-- <a-button class="btn_item" size2="small"  type="primary" @click="createNewPro">新建项目</a-button> -->
            </div>
          </div>
          <div class="tab_wrap">
            <a-tabs v-model:activeKey="activeKey" @change="changeTab">
               <a-tab-pane :key="0" tab="光伏">
               光伏
              </a-tab-pane>
              <!--<a-tab-pane :key="1" tab="风机">
                风机
              </a-tab-pane> -->
              <a-tab-pane :key="3" tab="储能">
                <a-button style="margin: -10px 0 10px 0;" size="small"  @click="createNewItem(3)">新增</a-button>
                <a-table
                  size="small"
                  :loading="loading"
                  :pagination="false"
                  :columns="batColumns()" rowKey="id" :data-source="tableData"
                  :defaultExpandAllRows="true">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                      <div class="t_btn_wrap">
                        <a class="a_item" @click="toEditItem(record, 3)">修改</a>
                        <a class="a_item" @click="toDelItem(record, 3)">删除</a>
                      </div>
                    </template>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane :key="4" tab="电解槽">
                <a-button style="margin: -10px 0 10px 0;" size="small"  @click="createNewItem(4)">新增</a-button>
                <a-table
                  size="small"
                  :loading="loading"
                  :pagination="false"
                  :columns="alkColumns()" rowKey="id" :data-source="tableData"
                  :defaultExpandAllRows="true">
                  <template #bodyCell="{ column, record, text }">
                    <template v-if="column.key === 'action'">
                      <div class="t_btn_wrap">
                        <a href="void:0" class="a_item" @click="toEditItem(record, 4)">修改</a>
                        <a href="void:0" class="a_item" @click="toDelItem(record, 4)">删除</a>
                      </div>
                    </template>
                    <template v-if="column.key === 'ele_type'">
                      <a-tag :color="getOptionByValue(column.options, text)?.color">{{ getOptionByValue(column.options, text)?.label  }}</a-tag>
                    </template>
                   
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane :key="5" tab="储罐">
                <a-button style="margin: -10px 0 10px 0;" size="small"  @click="createNewItem(5)">新增</a-button>
                <a-table
                  size="small"
                  :loading="loading"
                  :pagination="false"
                  :columns="alkStoreColumns()" rowKey="id" :data-source="tableData"
                  :defaultExpandAllRows="true">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                      <div class="t_btn_wrap">
                        <a href="void:0" class="a_item" @click="toEditItem(record, 5)">修改</a>
                        <a href="void:0" class="a_item" @click="toDelItem(record, 5)">删除</a>
                      </div>
                    </template>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane :key="6" tab="电价">
                <GridPriceConfig />
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </div>
    </div>
    <a-modal
      v-model:open="visiableEditProjectModal" :title="isEditing?'修改设备':'创建设备'"
      @ok="submitEditAndNew"
      :confirm-loading="confirmLoading"
      destroyOnClose
      >
      <div>
        <a-form
          labelAlign="left2"
          ref="formRef"
          :model="formState"
          name="basic"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
        >
          <a-form-item
            v-for="item in curFormItems"
            :label="item.title"
            :name="item.dataIndex"
            :rules="item.rules"
          >
            <a-input-number
              style="width:100%"
              v-model:value="formState[item.dataIndex]" size="small"
              v-if="item.s_type === 'number'"  :min="0"
            />
            <a-input v-else-if="item.s_type==='string'" v-model:value="formState[item.dataIndex]" />
            <a-select
              v-else-if="item.s_type === 'select'"
              v-model:value="formState[item.dataIndex]"
              style2="width: 120px"
            >
              <a-select-option
                v-for="i in item.options"
                :value="i.value">{{ i.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, h } from 'vue'
import { message, Pagination, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { getProjects, getDevice, addDevParams, updateDevParams, deleteDevParams  } from '@/api/project'
import { useRouter } from 'vue-router'
import { pvColumns, windColumns, batColumns, alkColumns, alkStoreColumns } from './util'
import LineChart from '@/components/LineChart/index.vue'
import { formatDate, getOptionByValue } from '@/util'
import GridPriceConfig from './grid/index.vue'

import { cloneDeep } from 'lodash'
const [modal, contextHolder] = Modal.useModal();

const router = useRouter()
const loading = ref(false)
const visiableEditProjectModal = ref(false)
const formRef = ref()
const tableData = ref([])
const formState = ref({})
const curProjectItem = ref({})
const confirmLoading = ref(false)
const activeKey = ref(3)
const formItems = ref([])
const isEditing = ref(false)
const curFormItems = ref(batColumns().filter(i => i.s_type))

const getFormItem = (key) => {
  const t = [pvColumns, windColumns, function(){return []}, batColumns, alkColumns, alkStoreColumns]
  return t[key]().filter(i => i.s_type)
}
const toDelItem = (record, key) => {
  Modal.confirm({
    title: `确认删除?`,
    // icon: h(ExclamationCircleOutlined),
    // okText: 'Yes',
    // okType: 'danger',
    // cancelText: 'No',
    async onOk() {
      const { code, msg } = await deleteDevParams({ devIdList: [record.id] })
      if (code === 0) {
        message.success('删除成功')
        initData()
      } else {
        message.error(msg)
      }
    }
  });
  // TODO: 新建方案，不需要项目 id devIdList
}
const toEditItem = (record, key) => {
  visiableEditProjectModal.value = true
  isEditing.value = true


  curFormItems.value = getFormItem(key)
  curProjectItem.value = record
  formState.value = cloneDeep(record)
}

const createNewItem = (key) => {
  visiableEditProjectModal.value = true
  isEditing.value = false
  
  curFormItems.value = getFormItem(key)
  console.log('form item:', curFormItems.value, key)
}

const changeTab = (key) => {
  activeKey.value = key
  initData()
}

const submitEditAndNew = async () => {
  const values = await formRef.value.validateFields()
  const type = activeKey.value 
  const baseInfo = {
    manufacturer: formState.value.manufacturer,
    model: formState.value.model
  }
  let code, msg;
  confirmLoading.value = true
  const params = cloneDeep(formState.value)

  delete params.manufacturer
  delete params.model
  delete params.id 
  delete params.category

  if (isEditing.value) {
    baseInfo.id = curProjectItem.value?.id
   
    const r = await updateDevParams([{
      type,
      baseInfo,
      params
    }])
    code = r.code
    msg = r.msg
  } else {
    const r = await addDevParams([{
      type,
      baseInfo,
      params
    }])
    code = r.code
    msg = r.msg
  }
  confirmLoading.value = false

  
  if (code === 0) {
    // message.success('已成功提交')
    initData()
  } else {
    message.error(msg)
  }
  visiableEditProjectModal.value = false
}

const initData = async (params) => {
  loading.value = true
  const { code, data, msg } = await getDevice({ type: activeKey.value })
  if (code === 0) {
    tableData.value = data.result.map(i => {
      const { baseInfo, params } = i
      return {
        ...baseInfo,
        ...params
      }
    })
    console.log('table:', tableData.value)
  } else {
    message.error(msg)
  }
  loading.value = false
}

onMounted(() => {
  initData()
})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  font-size: 12px;
  padding: 10px 20px;
  position: relative;
  // * {
  //   font-size: 12px;
  // }
}
.p_wrap {
  .title_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn_wrap {
      .btn_item {
        margin-left: 10px;
      }
    }
  }
}
.content_wrap {
  margin-top: 20px;
  .p_title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 15px;
  }
  .tab_wrap {
    background: #fff;
    padding: 10px 15px;
    .t_btn_wrap {
      .a_item {
        margin-left: 10px;
      }
    }
  }

}

</style>
